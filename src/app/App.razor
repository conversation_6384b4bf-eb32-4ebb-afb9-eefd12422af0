@using Truckify.App.Features.Nav
@using Truckify.App.Features.Auth

@if (IsAuthenticated)
{
    <!-- Dashboard Layout -->
    <div class="bg-[#f8f8f8] flex h-screen">
        <aside class="fixed z-50 md:relative">
            <!-- Sidebar -->
            <input type="checkbox" class="peer hidden" id="sidebar-open" />
            <label class="peer-checked:rounded-full peer-checked:p-2 peer-checked:right-6 peer-checked:bg-gray-600 peer-checked:text-white absolute top-3 z-20 mx-4 cursor-pointer md:hidden" for="sidebar-open">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </label>
            <nav id="sidebar-nav" aria-label="Sidebar Navigation" class="peer-checked:w-64 left-0 z-10 flex h-screen w-0 flex-col overflow-hidden bg-[#2f3337] text-white transition-all md:h-screen md:w-64 lg:w-56">
                <div class="bg-[#2f3337] py-4 pl-10">
                    <span class="">
                        <span class="mr-1 inline-flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 align-bottom text-2xl font-bold">T</span>
                        <span class="text-xl">ruckify</span>
                    </span>
                </div>
                <!-- Divider aligned with top bar -->
                <div class="border-t border-gray-600 w-full"></div>
                <NavItems />

                <!-- User Profile Section -->
                <div class="my-6 mt-auto ml-10 flex cursor-pointer">
                    <div>
                        <div class="h-12 w-12 rounded-full bg-blue-600 flex items-center justify-center text-white font-bold text-lg">
                            @(CurrentUser?.Substring(0, 1).ToUpper() ?? "U")
                        </div>
                    </div>
                    <div class="ml-3">
                        <p class="font-medium">@CurrentUser</p>
                        <p class="text-sm text-gray-300">Administrator</p>
                    </div>
                </div>
            </nav>
        </aside>
        <!-- /Sidebar -->

        <div class="flex h-full w-full flex-col">
            <!-- Navbar -->
            <header class="flex-shrink-0 relative flex flex-col items-center bg-white px-4 py-4 shadow sm:flex-row md:h-16">
                <div class="flex w-full flex-col justify-end overflow-hidden transition-all sm:max-h-full sm:flex-row sm:items-center">
                    <ul class="mx-auto flex space-x-6 sm:mx-5 sm:mt-0">
                        <li class="">
                            <button class="flex h-8 w-8 items-center justify-center rounded-xl border text-gray-600 hover:text-black hover:shadow cursor-pointer">
                                @*<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>*@
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                                </svg>
                            </button>
                        </li>
                        <li class="">
                            <button class="flex h-8 w-8 items-center justify-center rounded-xl border text-gray-600 hover:text-black hover:shadow cursor-pointer">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                                </svg>
                            </button>
                        </li>
                        <li class="">
                            <button class="flex h-8 w-8 items-center justify-center rounded-xl border text-gray-600 hover:text-black hover:shadow cursor-pointer">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </button>
                        </li>
                    </ul>
                </div>
            </header>
            <!-- /Navbar -->

            <!-- Main -->
            <div class="h-full overflow-hidden">
                <main id="dashboard-main" class="h-full overflow-auto px-6 py-6">
                    <div id="mainContentArea" hx-get="@CurrentRoute" hx-trigger="load">
                        <!-- Content will be loaded here -->
                    </div>
                </main>
            </div>
            <!-- /Main -->
        </div>
    </div>
}
else
{
    <!-- Show Login Page -->
    <LoginPage Model="@LoginModel" />
}

@code {
    [Inject] public NavigationManager? NavigationManager { get; set; }
    [Inject] public IHttpContextAccessor? HttpContextAccessor { get; set; }
    [Inject] public EndpointDataSource? EndpointDataSource { get; set; }

    private string CurrentRoute = string.Empty;
    private static List<string> ValidRoutes = [];

    private bool IsAuthenticated => HttpContextAccessor?.HttpContext?.User?.Identity?.IsAuthenticated ?? false;
    private string? CurrentUser => HttpContextAccessor?.HttpContext?.User?.Identity?.Name;
    private LoginForm.LoginFormModel LoginModel = new();

    protected override void OnInitialized()
    {
        ValidRoutes = EndpointDataSource?.Endpoints
            .OfType<RouteEndpoint>()
            .Where(endpoint => !string.IsNullOrWhiteSpace(endpoint.RoutePattern.RawText))
            .Select(endpoint => endpoint.RoutePattern.RawText!)
            .ToList() ?? [];

        var request = HttpContextAccessor?.HttpContext?.Request;
        var requestCurrentUrl = request?.Headers["hx-current-url"].ToString() ?? string.Empty;
        var requestedRoute = "/" + requestCurrentUrl.Replace(NavigationManager!.BaseUri, string.Empty);

        CurrentRoute = requestedRoute switch
        {
            "" or "/" => "/home",
            _ when !ValidRoutes.Contains(requestedRoute) => "/notfound",
            _ => requestedRoute
        };
    }
}