<div id="admin-dashboard">
    <h1 class="text-2xl font-bold">Welcome to your dashboard, <b>@GetUsername()</b>!</h1>

    <br />
    <hr />
    <br />

    <h2 class="text-2xl font-bold">Disable / enable features</h2>
    <p>(visual demonstration only. In real life you would perists these settings)</p>
    <br />
    @foreach (var featureFlag in Enum.GetValues(typeof(FeatureFlag)))
    {
        var isFeatureEnabled = Configuration?.GetValue<bool>($"Features:{featureFlag}:Enabled") ?? false;

        <div class="flex flex-row">
            <div class="basis-3/4">
                @featureFlag.ToString()
            </div>
            <div class="basis-1/4">
                <select hx-post="/admin/feature-toggle" hx-trigger="change" hx-target="#admin-dashboard" hx-ext='json-enc' class="bg-gray-50 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600">
                    <option selected>@(isFeatureEnabled ? "True" : "False")</option>
                    <option value="@isFeatureEnabled">@(!isFeatureEnabled)</option>
                </select>
            </div>
        </div>
        <br />
    }
</div>

@code 
{
    [Inject] public IHttpContextAccessor? HttpContextAccessor { get; set; }
    [Inject] public IConfiguration? Configuration { get; set; }

    string GetUsername() => HttpContextAccessor?.HttpContext?.User?.Identity?.IsAuthenticated == true
        ? HttpContextAccessor?.HttpContext?.User.Claims.First(claim => claim.Type == ClaimTypes.Name).Value!
        : "anonymous";

    
}

