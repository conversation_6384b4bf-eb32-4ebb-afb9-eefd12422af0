@using System.Text.Json
@using Truckify.App.Infrastructure.Components

<!-- Modal Backdrop -->
<div id="userModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <!-- <PERSON><PERSON> Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">@Model.Title</h3>
            <button onclick="removeElement('#userModal')"
                    class="text-gray-400 hover:text-gray-600 p-1">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="p-6">
            <form id="userForm"
                  hx-post="@(Model.Form.IsEdit ? $"/users/{Model.Form.Id}" : "/users")"
                  hx-ext="json-enc,response-targets"
                  hx-target="#userModal"
                  hx-target-error="#toast-container"
                  hx-swap="outerHTML"
                  hx-on::after-request="removeElement('#userModal'); htmx.ajax('GET', '/users', {target: '#users-list', swap: 'outerHTML'})">
                    
                    @if (Model.Form.IsEdit)
                    {
                        <input type="hidden" name="Id" value="@Model.Form.Id" />
                    }

                <div class="space-y-4">
                        <!-- First Name -->
                        <div>
                            <label for="firstName" class="block text-sm font-medium text-gray-700 mb-2">
                                First Name
                            </label>
                            <input type="text" 
                                   id="firstName"
                                   name="FirstName" 
                                   value="@Model.Form.FirstName"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @(ValidationContext.TryGetError(nameof(Model.Form.FirstName), out _) ? "border-red-500" : "")"
                                   placeholder="Enter first name">
                            @if (ValidationContext.TryGetError(nameof(Model.Form.FirstName), out var firstNameError))
                            {
                                <p class="mt-1 text-sm text-red-600">@firstNameError.ErrorMessage</p>
                            }
                        </div>

                        <!-- Last Name -->
                        <div>
                            <label for="lastName" class="block text-sm font-medium text-gray-700 mb-2">
                                Last Name
                            </label>
                            <input type="text" 
                                   id="lastName"
                                   name="LastName" 
                                   value="@Model.Form.LastName"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @(ValidationContext.TryGetError(nameof(Model.Form.LastName), out _) ? "border-red-500" : "")"
                                   placeholder="Enter last name">
                            @if (ValidationContext.TryGetError(nameof(Model.Form.LastName), out var lastNameError))
                            {
                                <p class="mt-1 text-sm text-red-600">@lastNameError.ErrorMessage</p>
                            }
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                Email
                            </label>
                            <input type="email" 
                                   id="email"
                                   name="Email" 
                                   value="@Model.Form.Email"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 @(ValidationContext.TryGetError(nameof(Model.Form.Email), out _) ? "border-red-500" : "")"
                                   placeholder="Enter email address">
                            @if (ValidationContext.TryGetError(nameof(Model.Form.Email), out var emailError))
                            {
                                <p class="mt-1 text-sm text-red-600">@emailError.ErrorMessage</p>
                            }
                        </div>
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="flex justify-end space-x-3 px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                        <button type="button"
                                class="inline-flex items-center justify-center font-medium cursor-pointer select-none border focus:outline-none focus:ring-0 transition-colors px-4 py-2 text-sm rounded-md bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:bg-gray-50 focus:border-gray-400 active:bg-gray-100 active:border-gray-500"
                                onclick="removeElement('#userModal')">
                            Cancel
                        </button>
            <button type="submit"
                    form="userForm"
                    class="inline-flex items-center justify-center font-medium cursor-pointer select-none border focus:outline-none focus:ring-0 transition-colors px-4 py-2 text-sm rounded-md bg-gray-900 border-gray-900 text-white hover:bg-gray-800 hover:border-gray-800 focus:bg-gray-800 focus:border-gray-800 active:bg-gray-700 active:border-gray-700">
                @(Model.Form.IsEdit ? "Update User" : "Create User")
            </button>
        </div>
    </div>
</div>

@code
{
    [Inject] public ValidationContext ValidationContext { get; set; } = new();
    [Parameter] public UserModalModel Model { get; set; } = new();

    public record UserModalModel
    {
        public string Title { get; set; } = string.Empty;
        public UserFormModel Form { get; set; } = new();
    }

    public record UserFormModel
    {
        public string Id { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public bool IsEdit { get; set; } = false;
    }
}
