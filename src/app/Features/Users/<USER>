@using Truckify.App.Domain
@using Truckify.App.Infrastructure.Data
@using Truckify.App.Infrastructure.Components
@using System.Text.Json

<h1 class="text-2xl font-black text-gray-800">Users</h1>

<!-- Table Section -->
<div id="users-list" class="w-full px-4 py-6 sm:px-6 lg:px-8">
    <!-- Card -->
    <div class="flex flex-col">
        <div class="-m-1.5 overflow-x-auto">
            <div class="p-1.5 min-w-full inline-block align-middle">
                <div class="bg-white border border-gray-200 rounded-xl shadow-2xs overflow-hidden">
                    <!-- Header -->
                    <div class="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-b border-gray-200">
                        <!-- Input -->
                        <div class="sm:col-span-1">
                            <label for="hs-as-table-product-review-search" class="sr-only">Search</label>
                            <div class="relative">
                                <input type="text"
                                       id="hs-as-table-product-review-search"
                                       placeholder="Search users..."
                                       value="@Model.Search"
                                       class="py-2 px-3 ps-11 block w-full border border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
                                       hx-get="/users"
                                       hx-trigger="keyup changed delay:300ms"
                                       hx-target="#users-list"
                                       hx-swap="outerHTML"
                                       hx-include="[name='page']"
                                       name="search">
                                <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-4">
                                    <svg class="size-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <!-- End Input -->

                        <div class="sm:col-span-2 md:grow">
                            <div class="flex justify-end gap-x-2">
                                <Button Variant="primary"
                                        Size="md"
                                        IconLeft="fas fa-plus"
                                        hx-get="/users/create"
                                        hx-target="body"
                                        hx-swap="beforeend">
                                    Add User
                                </Button>
                            </div>
                        </div>
                    </div>

                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="ps-6 py-3 text-start">
                                <div class="flex items-center gap-x-2">
                                    <span class="text-xs font-semibold uppercase text-gray-800">
                                        Name
                                    </span>
                                </div>
                            </th>

                            <th scope="col" class="px-6 py-3 text-start">
                                <div class="flex items-center gap-x-2">
                                    <span class="text-xs font-semibold uppercase text-gray-800">
                                        Email
                                    </span>
                                </div>
                            </th>

                            <th scope="col" class="px-6 py-3 text-end"></th>
                        </tr>
                        </thead>

                        <tbody class="divide-y divide-gray-200">
                        @if (Model.PaginatedUsers.Items.Any())
                        {
                            @foreach (var user in Model.PaginatedUsers.Items)
                            {
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">@user.FullName</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">@user.Email</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex justify-end gap-2">
                                            <TableActionButton Icon="ri-pencil-line"
                                                               Variant="outline"
                                                               Size="sm"
                                                               Title="Edit user"
                                                               HxGet="@($"/users/{user.Id}/edit")"
                                                               HxTarget="body"
                                                               HxSwap="beforeend"/>

                                            <TableActionButton Icon="ri-delete-bin-6-line"
                                                               Variant="danger-outline"
                                                               Size="sm"
                                                               Title="Delete user"
                                                               HxDelete="@($"/users/{user.Id}")"
                                                               HxTarget="#users-list"
                                                               HxSwap="outerHTML"
                                                               HxConfirm="Are you sure you want to delete this user?"/>
                                        </div>
                                    </td>
                                </tr>
                            }
                        }
                        else
                        {
                            <tr>
                                <td colspan="3" class="px-6 py-4 text-center text-gray-500">
                                    @if (!string.IsNullOrEmpty(Model.Search))
                                    {
                                        <span>No users found matching "@Model.Search"</span>
                                    }
                                    else
                                    {
                                        <span>No users found</span>
                                    }
                                </td>
                            </tr>
                        }
                        </tbody>
                    </table>

                    <!-- Pagination -->
                    @if (Model.PaginatedUsers.TotalPages > 1)
                    {
                        <div class="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-t border-gray-200">
                            <div class="max-w-sm space-y-3">
                                <div class="text-sm text-gray-600">
                                    Showing @((Model.PaginatedUsers.PageNumber - 1) * Model.PaginatedUsers.PageSize + 1) to @(Math.Min(Model.PaginatedUsers.PageNumber * Model.PaginatedUsers.PageSize, Model.PaginatedUsers.TotalItems)) of @Model.PaginatedUsers.TotalItems results
                                </div>
                            </div>

                            <div>
                                <div class="inline-flex gap-x-2">
                                    <!-- Previous Button -->
                                    <Button Variant="outline"
                                            Size="sm"
                                            IconLeft="ri-arrow-left-s-line"
                                            Disabled="@(!Model.PaginatedUsers.HasPreviousPage)"
                                            hx-get="/users"
                                            hx-vals='@JsonSerializer.Serialize(new { search = Model.Search, page = Model.PaginatedUsers.PageNumber - 1, pageSize = Model.PaginatedUsers.PageSize })'
                                            hx-target="#users-list"
                                            hx-swap="outerHTML">
                                        Prev
                                    </Button>

                                    <!-- Page Numbers -->
                                    @for (int i = Math.Max(1, Model.PaginatedUsers.PageNumber - 2); i <= Math.Min(Model.PaginatedUsers.TotalPages, Model.PaginatedUsers.PageNumber + 2); i++)
                                    {
                                        var pageNumber = i; // Create local copy to avoid closure issues
                                        <Button Variant="@(pageNumber == Model.PaginatedUsers.PageNumber ? "primary" : "outline")"
                                                Size="sm"
                                                hx-get="/users"
                                                hx-vals='@JsonSerializer.Serialize(new { search = Model.Search, page = pageNumber, pageSize = Model.PaginatedUsers.PageSize })'
                                                hx-target="#users-list"
                                                hx-swap="outerHTML">
                                            @pageNumber
                                        </Button>
                                    }

                                    <!-- Next Button -->
                                    <Button Variant="outline"
                                            Size="sm"
                                            IconRight="ri-arrow-right-s-line"
                                            Disabled="@(!Model.PaginatedUsers.HasNextPage)"
                                            hx-get="/users"
                                            hx-vals='@JsonSerializer.Serialize(new { search = Model.Search, page = Model.PaginatedUsers.PageNumber + 1, pageSize = Model.PaginatedUsers.PageSize })'
                                            hx-target="#users-list"
                                            hx-swap="outerHTML">
                                        Next
                                    </Button>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Hidden input for current page (used by search) -->
                    <input type="hidden" name="page" value="@Model.PaginatedUsers.PageNumber"/>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Toast Container -->
<div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

<!-- Error Container for HTMX response-targets -->
<div id="errors"></div>

<!-- Minimal JavaScript utilities -->
<script>
    function removeElement(selector) {
        let element = document.querySelector(selector);
        if (element) element.remove();
    }

    function removeElementWithDelay(selector, delay = 2000) {
        setTimeout(() => removeElement(selector), delay);
    }
</script>

@code
{
    [Inject] public ValidationContext ValidationContext { get; set; } = new();
    [Parameter] public UsersListModel Model { get; set; } = new();

    public record UsersListModel
    {
        public PaginatedList<User> PaginatedUsers { get; set; } = new();
        public string Search { get; set; } = string.Empty;
        public string ToJson() => JsonSerializer.Serialize(this);
    }
}
