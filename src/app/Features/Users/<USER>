using Microsoft.AspNetCore.Mvc;
using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Truckify.App.Domain;
using Truckify.App.Infrastructure.Data;
using Truckify.App.Infrastructure.Common;
using static Truckify.App.Features.Users.UsersList;
using static Truckify.App.Features.Users.UserModal;
using Truckify.App.Infrastructure.Components;

namespace Truckify.App.Features.Users;

public class UsersEndpoint
{
    public record GetUsersRequest(string? Search = null, int Page = 1, int PageSize = 10);
    public record CreateUserRequest(string FirstName, string LastName, string Email);
    public record UpdateUserRequest(string FirstName, string LastName, string Email)
    {
        public string Id { get; init; } = string.Empty;
    }


    public static async Task<IResult> GetUsers([AsParameters] GetUsersRequest request, [FromServices] IDocumentStore documentStore)
    {
        using var session = documentStore.OpenAsyncSession();
        var result = await UserBusinessLogic.GetUsersAsync(session, request.Search, request.Page, request.PageSize);

        return Component<UsersList, UsersListModel>(model: new()
        {
            PaginatedUsers = result.PaginatedUsers,
            Search = request.Search ?? string.Empty
        });
    }

    public static IResult GetCreateUserForm()
    {
        return Component<UserModal, UserModalModel>(model: new()
        {
            Title = "Add User",
            Form = new UserFormModel { IsEdit = false }
        });
    }

    public static async Task<IResult> GetEditUserForm([FromRoute] string id, [FromServices] IDocumentStore documentStore)
    {
        using var session = documentStore.OpenAsyncSession();
        var user = await UserBusinessLogic.GetUserByIdAsync(session, id);

        if (user == null)
            return Results.NotFound();

        return Component<UserModal, UserModalModel>(model: new()
        {
            Title = "Edit User",
            Form = new UserFormModel
            {
                Id = user.Id,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Email = user.Email,
                IsEdit = true
            }
        });
    }

    public static async Task<IResult> CreateUser(
        [FromBody] CreateUserRequest request,
        ValidationContext validationContext,
        [FromServices] IDocumentStore documentStore,
        HttpContext httpContext)
    {
        if (validationContext.Errors.Any())
        {
            return Component<UserModal, UserModalModel>(model: new()
            {
                Title = "Add User",
                Form = new UserFormModel
                {
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    Email = request.Email,
                    IsEdit = false
                }
            });
        }

        using var session = documentStore.OpenAsyncSession();
        var result = await UserBusinessLogic.CreateUserAsync(session, request.FirstName, request.LastName, request.Email);

        if (!result.Success)
        {
            // Return modal with validation errors (200 status)
            return Component<UserModal, UserModalModel>(model: new()
            {
                Title = "Add User",
                Form = new UserFormModel
                {
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    Email = request.Email,
                    IsEdit = false,
                    ErrorMessage = result.ErrorMessage
                }
            });
        }

        // Return updated users list with success trigger
        var usersResult = await UserBusinessLogic.GetUsersAsync(session, pageSize: 10);

        httpContext.Response.Headers["HX-Trigger-After-Swap"] = """{"showCreateSuccess": {"message": "User created successfully!"}}""";

        return Component<UsersList, UsersListModel>(model: new()
        {
            PaginatedUsers = usersResult.PaginatedUsers,
            Search = string.Empty
        });
    }

    public static async Task<IResult> UpdateUser(
        [FromRoute] string id,
        [FromBody] UpdateUserRequest request,
        ValidationContext validationContext,
        [FromServices] IDocumentStore documentStore,
        HttpContext httpContext)
    {
        // Set the ID from the route
        request = request with { Id = id };

        if (validationContext.Errors.Any())
        {
            return Component<UserModal, UserModalModel>(model: new()
            {
                Title = "Edit User",
                Form = new UserFormModel
                {
                    Id = request.Id,
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    Email = request.Email,
                    IsEdit = true
                }
            });
        }

        using var session = documentStore.OpenAsyncSession();
        var result = await UserBusinessLogic.UpdateUserAsync(session, id, request.FirstName, request.LastName, request.Email);

        if (!result.Success)
        {
            // Return modal with validation errors (200 status)
            return Component<UserModal, UserModalModel>(model: new()
            {
                Title = "Edit User",
                Form = new UserFormModel
                {
                    Id = request.Id,
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    Email = request.Email,
                    IsEdit = true,
                    ErrorMessage = result.ErrorMessage
                }
            });
        }

        // Return updated users list with success trigger
        var usersResult = await UserBusinessLogic.GetUsersAsync(session, pageSize: 10);

        httpContext.Response.Headers["HX-Trigger-After-Swap"] = """{"showUpdateSuccess": {"message": "User updated successfully!"}}""";

        return Component<UsersList, UsersListModel>(model: new()
        {
            PaginatedUsers = usersResult.PaginatedUsers,
            Search = string.Empty
        });
    }

    public static async Task<IResult> DeleteUser([FromRoute] string id, [FromServices] IDocumentStore documentStore, HttpContext httpContext)
    {
        using var session = documentStore.OpenAsyncSession();
        var result = await UserBusinessLogic.DeleteUserAsync(session, id);

        if (!result.Success)
            return Results.NotFound();

        // Return updated users list with success trigger
        var usersResult = await UserBusinessLogic.GetUsersAsync(session, pageSize: 10);

        httpContext.Response.Headers["HX-Trigger-After-Swap"] = """{"showDeleteSuccess": {"message": "User deleted successfully!"}}""";

        return Component<UsersList, UsersListModel>(model: new()
        {
            PaginatedUsers = usersResult.PaginatedUsers,
            Search = string.Empty
        });
    }
}
