using Raven.Client.Documents;
using Raven.Client.Documents.Linq;
using Raven.Client.Documents.Session;
using Truckify.App.Domain;
using Truckify.App.Infrastructure.Data;
using Truckify.App.Infrastructure.Common;

namespace Truckify.App.Features.Users;

public static class UserBusinessLogic
{
    public record PaginatedUsersResult
    {
        public PaginatedList<User> PaginatedUsers { get; init; } = new();
    }

    public static async Task<PaginatedUsersResult> GetUsersAsync(
        IAsyncDocumentSession session, 
        string? search = null, 
        int page = 1, 
        int pageSize = 10)
    {
        var query = session.Query<User>();
        
        if (!string.IsNullOrWhiteSpace(search))
        {
            query = query.Search(x => x.FirstName, $"{search}*");
            query = query.Search(x => x.LastName, $"{search}*");
            query = query.Search(x => x.Email, $"{search}*");
        }
        
        var users = await query
            .Statistics(out var stats)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        var paginatedUsers = new PaginatedList<User>
        {
            Items = users,
            PageNumber = page,
            PageSize = pageSize,
            TotalItems = stats.TotalResults
        };

        return new PaginatedUsersResult { PaginatedUsers = paginatedUsers };
    }

    public static async Task<User?> GetUserByIdAsync(IAsyncDocumentSession session, string id)
    {
        return await session.LoadAsync<User>(id);
    }

    public static async Task<Result<User>> CreateUserAsync(
        IAsyncDocumentSession session,
        string firstName,
        string lastName,
        string email)
    {
        // Check for duplicate email
        var existingUser = await session.Query<User>()
            .Where(u => u.Email == email)
            .FirstOrDefaultAsync();

        if (existingUser != null)
        {
            return Result<User>.ErrorResult("A user with this email already exists");
        }

        var userResult = User.Create(firstName, lastName, email);
        if (!userResult.Success)
        {
            return Result<User>.ErrorResult(userResult.ErrorMessage!);
        }

        await session.StoreAsync(userResult.Value);
        await session.SaveChangesAsync();

        return Result<User>.SuccessResult(userResult.Value!);
    }

    public static async Task<Result<User>> UpdateUserAsync(
        IAsyncDocumentSession session,
        string id,
        string firstName,
        string lastName,
        string email)
    {
        var user = await session.LoadAsync<User>(id);
        if (user == null)
        {
            return Result<User>.ErrorResult("User not found");
        }

        // Check for duplicate email (excluding current user)
        var existingUser = await session.Query<User>()
            .Where(u => u.Email == email && u.Id != id)
            .FirstOrDefaultAsync();

        if (existingUser != null)
        {
            return Result<User>.ErrorResult("A user with this email already exists");
        }

        var updateResult = user.UpdateDetails(firstName, lastName, email);
        if (!updateResult.Success)
        {
            return Result<User>.ErrorResult(updateResult.ErrorMessage!);
        }

        await session.SaveChangesAsync();

        return Result<User>.SuccessResult(user);
    }

    public static async Task<Result<User>> DeleteUserAsync(IAsyncDocumentSession session, string id)
    {
        var user = await session.LoadAsync<User>(id);
        if (user == null)
        {
            return Result<User>.ErrorResult("User not found");
        }

        session.Delete(user);
        await session.SaveChangesAsync();

        return Result<User>.SuccessResult(user);
    }
}
