<div id="counter">
    <div class="flex flex-column justify-center">

        <!-- Decrement Button -->
        <button class="p-3"
                hx-post="counter/update"
                hx-trigger="click"
                hx-target="#counter"
                hx-swap="outerHTML"
                hx-ext='json-enc'
                hx-vals='@((Model with { Count = Model.Count - 1 }).ToJson())'>
            <span class="text-3xl"><i class="fas fa-arrow-left"></i></span>
        </button>

        <!-- Counter Value -->
        <div class="px-2 w-24 text-center">
            <span class="text-5xl font-bold">
                @Model.Count
            </span>
        </div>

        <!-- Increment Button -->
        <button class="p-3"
                hx-post="counter/update"
                hx-trigger="click"
                hx-target="#counter"
                hx-swap="outerHTML"
                hx-ext='json-enc'
                hx-vals='@((Model with { Count = Model.Count + 1 }).ToJ<PERSON>())'>
            <span class="text-3xl"><i class="fas fa-arrow-right"></i></span>
        </button>
    </div>

    <!-- Validation Message -->
    @if (ValidationContext.TryGetError(nameof(Model.Count), out var error))
    {
        <p class="text-red-500 mt-4 text-center">@error.ErrorMessage</p>
    }

    <br />

    <p>
        Try incrementing or decrementing the counter value using the buttons above.
        When values get too high or too low, an error message will be displayed.
    </p>
</div>

@code
{
    [Inject] public ValidationContext ValidationContext { get; set; } = new();

    [Parameter] public CounterModel Model { get; set; } = new();

    public record CounterModel
    {
        public int Count { get; set; }
        public string ToJson() => JsonSerializer.Serialize(this);
    }
}