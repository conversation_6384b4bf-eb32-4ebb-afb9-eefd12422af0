<ul class="flex flex-col py-4 space-y-1">
    <li class="px-5">
        <div class="flex flex-row items-center h-8">
            <div class="text-sm font-light tracking-wide text-gray-400">Menu</div>
        </div>
    </li>
    <li>
        <a hx-get="/home"
           hx-push-url="/"
           hx-trigger="click"
           hx-target="#mainContentArea"
           hx-swap="innerHTML"
           onclick="setActiveMenuItem(this)"
           data-route="/home"
           class="nav-item @GetNavItemClass("/home")">
            <span class="inline-flex justify-center items-center ml-4">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                </svg>
            </span>
            <span class="ml-2 text-sm tracking-wide truncate">Home</span>
        </a>
    </li>
    <li>
        <a hx-get="/how-it-works"
           hx-push-url="true"
           hx-trigger="click"
           hx-target="#mainContentArea"
           hx-swap="innerHTML"
           onclick="setActiveMenuItem(this)"
           data-route="/how-it-works"
           class="nav-item @GetNavItemClass("/how-it-works")">
            <span class="inline-flex justify-center items-center ml-4">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </span>
            <span class="ml-2 text-sm tracking-wide truncate">How it works</span>
        </a>
    </li>

    <li class="px-5">
        <div class="flex flex-row items-center h-8">
            <div class="text-sm font-light tracking-wide text-gray-400">Demo</div>
        </div>
    </li>
    <li>
        <a hx-get="/counter"
           hx-push-url="true"
           hx-trigger="click"
           hx-target="#mainContentArea"
           hx-swap="innerHTML"
           hx-ext='json-enc'
           onclick="setActiveMenuItem(this)"
           data-route="/counter"
           class="nav-item @GetNavItemClass("/counter")">
            <span class="inline-flex justify-center items-center ml-4">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h4a1 1 0 011 1v2m3 0V1.5A1.5 1.5 0 0014.5 0h-5A1.5 1.5 0 008 1.5V4m8 0H4a2 2 0 00-2 2v11a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2z"></path>
                </svg>
            </span>
            <span class="ml-2 text-sm tracking-wide truncate">Counter</span>
        </a>
    </li>
    <li>
        <a hx-get="/form"
           hx-push-url="true"
           hx-trigger="click"
           hx-target="#mainContentArea"
           hx-swap="innerHTML"
           hx-ext='json-enc'
           onclick="setActiveMenuItem(this)"
           data-route="/form"
           class="nav-item @GetNavItemClass("/form")">
            <span class="inline-flex justify-center items-center ml-4">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </span>
            <span class="ml-2 text-sm tracking-wide truncate">Form</span>
        </a>
    </li>

    @if (HttpContextAccessor?.HttpContext?.User.Identity?.IsAuthenticated == true)
    {
        <li class="px-5">
            <div class="flex flex-row items-center h-8">
                <div class="text-sm font-light tracking-wide text-gray-400">Management</div>
            </div>
        </li>
        <li>
            <a hx-get="/users"
               hx-push-url="true"
               hx-trigger="click"
               hx-target="#mainContentArea"
               hx-swap="innerHTML"
               hx-ext='json-enc'
               onclick="setActiveMenuItem(this)"
               data-route="/users"
               class="nav-item @GetNavItemClass("/users")">
                <span class="inline-flex justify-center items-center ml-4">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </span>
                <span class="ml-2 text-sm tracking-wide truncate">Users</span>
            </a>
        </li>

        <li class="px-5">
            <div class="flex flex-row items-center h-8">
                <div class="text-sm font-light tracking-wide text-gray-400">Settings</div>
            </div>
        </li>
        <li>
            <a hx-get="/admin/dashboard"
               hx-push-url="true"
               hx-trigger="click"
               hx-target="#mainContentArea"
               hx-swap="innerHTML"
               hx-ext='json-enc'
               onclick="setActiveMenuItem(this)"
               data-route="/admin/dashboard"
               class="nav-item @GetNavItemClass("/admin/dashboard")">
                <span class="inline-flex justify-center items-center ml-4">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </span>
                <span class="ml-2 text-sm tracking-wide truncate">Admin Dashboard</span>
            </a>
        </li>
        <li>
            <a hx-get="/logout"
               hx-push-url="/"
               hx-trigger="click"
               hx-target="body"
               hx-swap="outerHTML"
               class="relative flex flex-row items-center h-11 focus:bg-slate-600 hover:bg-slate-600 text-gray-300 focus:outline-none border-l-4 border-transparent hover:border-indigo-500 pr-6 cursor-pointer">
                <span class="inline-flex justify-center items-center ml-4">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                </span>
                <span class="ml-2 text-sm tracking-wide truncate">Logout</span>
            </a>
        </li>
    }
</ul>

<script>
function setActiveMenuItem(clickedElement) {
    // Remove active classes from all nav items
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('bg-slate-600', 'border-indigo-500');
        item.classList.add('border-transparent');
    });

    // Add active classes to clicked item
    clickedElement.classList.add('bg-slate-600', 'border-indigo-500');
    clickedElement.classList.remove('border-transparent');
}
</script>

@code
{
    [Inject] public IHttpContextAccessor? HttpContextAccessor { get; set; }
    [Inject] public NavigationManager? NavigationManager { get; set; }

    private string GetNavItemClass(string route)
    {
        var baseClass = "relative flex flex-row items-center h-11 focus:bg-slate-600 hover:bg-slate-600 text-gray-300 focus:outline-none border-l-4 pr-6 cursor-pointer";
        var currentPath = GetCurrentPath();

        // Check if this is the active route
        var isActive = IsActiveRoute(route, currentPath);

        if (isActive)
        {
            return baseClass + " bg-slate-600 border-indigo-500";
        }
        else
        {
            return baseClass + " border-transparent hover:border-indigo-500";
        }
    }

    private string GetCurrentPath()
    {
        var request = HttpContextAccessor?.HttpContext?.Request;
        var currentUrl = request?.Headers["hx-current-url"].ToString() ?? string.Empty;

        if (string.IsNullOrEmpty(currentUrl))
        {
            // Fallback to request path
            return request?.Path.Value ?? "/";
        }

        // Extract path from full URL
        var path = "/" + currentUrl.Replace(NavigationManager?.BaseUri ?? "", string.Empty);
        return path == "/" ? "/home" : path;
    }

    private bool IsActiveRoute(string route, string currentPath)
    {
        // Handle home route special case
        if (route == "/home" && (currentPath == "/" || currentPath == "/home"))
        {
            return true;
        }

        // Handle users route and sub-routes
        if (route == "/users" && currentPath.StartsWith("/users", StringComparison.OrdinalIgnoreCase))
        {
            return true;
        }

        return currentPath.Equals(route, StringComparison.OrdinalIgnoreCase);
    }
}
