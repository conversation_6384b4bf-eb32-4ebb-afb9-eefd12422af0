<form method="post"
      hx-post="login"
      hx-push-url="/"
      hx-ext="json-enc"
      hx-target="this"
      onsubmit="setReturnUrl()">

    <p class="text-lg">Login as administrator to get access to the admin dashboard</p>
    <p class="text-sm">(Note: valid login credentials are "admin" and "test")</p>

    <br />

    <div class="mb-4">
        <label class="block font-bold mb-2">Name</label>
        <input name="username"
               value="@Model.Username"
               class="w-full text-gray-900 px-4 py-2 border border-gray-300 rounded-md focus:outline-none"
               type="text"
               placeholder="e.g admin">
    </div>

    <div class="mb-4">
        <label class="block font-bold mb-2">Password</label>
        <input name="password"
               value="@Model.Password"
               class="w-full text-gray-900 px-4 py-2 border border-gray-300 rounded-md focus:outline-none"
               type="password"
               placeholder="****">
    </div>

    <!-- Hidden field to store the return URL -->
    <input type="hidden" name="returnUrl" id="returnUrl" />

    <button class="px-4 py-2 text-white font-bold rounded-md bg-blue-500 hover:bg-blue-600 focus:ring-2 focus:ring-blue-500">
        Login
    </button>

    @if (ValidationContext.Errors.Any())
    {
        <p class="text-red-500 mt-4">Login failed</p>
    }
</form>


<script>
    function setReturnUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        const returnUrl = urlParams.get('ReturnUrl');
        document.getElementById('returnUrl').value = returnUrl ? returnUrl : '/';
    }
</script>

@code
{
    [Inject] public ValidationContext ValidationContext { get; set; } = new();
    [Parameter] public LoginFormModel Model { get; set; } = new();

    public record LoginFormModel
    {
        public string? Username { get; set; }
        public string? Password { get; set; }
    }
}
