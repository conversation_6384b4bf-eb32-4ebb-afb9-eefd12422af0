@using Truckify.App.Features.Auth

<div id="login-form">
    <form method="post"
          hx-post="/login"
          hx-ext="json-enc"
          hx-target="#login-form"
          hx-swap="outerHTML"
          onsubmit="setReturnUrl()">

        <div class="space-y-6">
            <div>
                <label for="username" class="block text-sm font-medium text-gray-700">
                    Username
                </label>
                <input name="username"
                       id="username"
                       value="@Model.Username"
                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       type="text"
                       placeholder="Enter your username"
                       required>
                <p class="mt-1 text-xs text-gray-500">Use "admin" for demo</p>
            </div>

            <div>
                <label for="password" class="block text-sm font-medium text-gray-700">
                    Password
                </label>
                <input name="password"
                       id="password"
                       value="@Model.Password"
                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       type="password"
                       placeholder="Enter your password"
                       required>
                <p class="mt-1 text-xs text-gray-500">Use "test" for demo</p>
            </div>

            <!-- Hidden field to store the return URL -->
            <input type="hidden" name="returnUrl" id="returnUrl" />

            <div>
                <button type="submit" 
                        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Sign in
                </button>
            </div>

            @if (ValidationContext.Errors.Any())
            {
                <div class="rounded-md bg-red-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                Login failed
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <p>Please check your credentials and try again.</p>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </form>
</div>

<script>
    function setReturnUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        const returnUrl = urlParams.get('ReturnUrl');
        document.getElementById('returnUrl').value = returnUrl ? returnUrl : '/';
    }
</script>

@code
{
    [Inject] public ValidationContext ValidationContext { get; set; } = new();
    [Parameter] public LoginFormModel Model { get; set; } = new();

    public record LoginFormModel
    {
        public string? Username { get; set; }
        public string? Password { get; set; }
    }
}
