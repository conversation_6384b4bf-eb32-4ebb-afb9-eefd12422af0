@using Truckify.App.Features.Auth

<div class="min-h-screen bg-[#f8f8f8] flex items-center justify-center">
    <div class="max-w-md w-full space-y-8">
        <div class="text-center">
            <div class="flex justify-center mb-6">
                <span class="mr-1 inline-flex h-16 w-16 items-center justify-center rounded-full bg-blue-600 align-bottom text-4xl font-bold text-white">T</span>
            </div>
            <h2 class="text-3xl font-bold text-gray-900">
                Welcome to Truckify
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                Please sign in to your account
            </p>
        </div>
        
        <div class="bg-white rounded-lg shadow-md p-8">
            <LoginForm Model="@Model" />
        </div>
        
        <div class="text-center">
            <p class="text-xs text-gray-500">
                &copy; 2024 Truckify. All rights reserved.
            </p>
        </div>
    </div>
</div>

@code
{
    [Parameter] public LoginForm.LoginFormModel Model { get; set; } = new();
}
