using System.Security.Claims;
using Microsoft.AspNetCore.Authentication;
using static Truckify.App.Features.Auth.LoginForm;

namespace Truckify.App.Features.Auth;

public class LoginEndpoint
{
    public record LoginRequest(string Username, string Password, string? ReturnUrl = null);

    public static IResult GetLogin()
    {
        return Component<LoginPage>();
    }

    public static async Task<IResult> SubmitLogin(
        LoginRequest request, 
        ValidationContext validationContext, 
        HttpContext httpContext)
    {
        if (validationContext.Errors.Any())
        {
            return Component<LoginForm, LoginFormModel>(model: new()
            {
                Username = request.Username,
                Password = request.Password
            });
        }

        await httpContext.SignInAsync(Constants.TruckifyAuthCookie,
            new ClaimsPrincipal(
                new ClaimsIdentity(
                [
                    new(ClaimTypes.Name, request.Username)
                ],
                Constants.TruckifyAuthCookie)));

        // Redirect to home page after successful login
        httpContext.Response.Headers["HX-Redirect"] = "/";
        return Results.Ok();
    }
}
