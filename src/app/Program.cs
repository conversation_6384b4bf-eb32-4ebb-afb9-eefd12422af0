using FluentValidation;
using Microsoft.AspNetCore.Http.HttpResults;
using Raven.Client.Documents;
using Truckify.App;
using Truckify.App.Features.Admin;
using Truckify.App.Features.Auth;
using Truckify.App.Features.Counter;
using Truckify.App.Features.Form;
using Truckify.App.Features.Home;
using Truckify.App.Features.HowItWorks;
using Truckify.App.Features.Nav;
using Truckify.App.Features.Routing;
using Truckify.App.Features.Users;
using Truckify.App.Infrastructure.Auth;
using Truckify.App.Infrastructure.Cors;
using Truckify.App.Infrastructure.Data;
using Truckify.App.Infrastructure.EndpointFilters;
using Truckify.App.Infrastructure.Exceptions;
using Truckify.App.Infrastructure.FeatureFlags;
using Truckify.App.Infrastructure.Logging;
using Truckify.App.Infrastructure.Middleware;
using Truckify.App.Infrastructure.Rendering;
using Truckify.App.Infrastructure.ResponseCompression;
using Truckify.App.Infrastructure.StaticFiles;

Console.WriteLine("Startup begins");

var builder = WebApplication.CreateBuilder(new WebApplicationOptions
{
    Args = args,
    WebRootPath = "Infrastructure/StaticFiles",
});

builder.Services.ConfigureHttpJsonOptions(options =>
{
    options.SerializerOptions.Converters.Add(new StringToBooleanConverter());
    options.SerializerOptions.PropertyNameCaseInsensitive = true;
});

// Add server features
builder.AddLoggingFeature();
builder.AddCorsFeature();
builder.AddAuthFeature();
builder.AddResponseCompressionFeature();
builder.AddServerSideRenderingFeature();
builder.AddGlobalExceptionHandlingFeature();

// Initialize RavenDB DocumentStore
var documentStore = new DocumentStore
{
    Urls = new[] { "http://localhost:8082" },
    Database = "truckify"
}.Initialize();

builder.Services.AddSingleton<IDocumentStore>(documentStore);


builder.Services.AddScoped<ValidationContext>();
builder.Services.AddHttpContextAccessor();
builder.Services.AddSingleton<FeatureFlagService>();
builder.Services.AddValidatorsFromAssemblyContaining<Program>();

// Use server features
var app = builder.Build();
app.UseGlobalExceptionHandlingFeature();
app.UseCorsFeature();
app.UseStaticFilesFeature();
app.UseHttpsRedirection();
app.UseAuthFeature();
app.UseMiddleware<HtmxRequestMiddleware>();

// Add global request filters etc.
var routes = app.MapGroup("")
   .AddEndpointFilter<RequestLoggingFilter>();

// Add routes
routes.AddRoutePath(HTTP.GET, "/notfound", RoutingEndpoint.NotFound)
      .AllowAnonymous();

routes.AddRoutePath(HTTP.GET, "/home", HomeEndpoint.GetHome)
      .Produces<RazorComponentResult<Home>>()
      .AllowAnonymous()
      .WithSummary("Get the home page content");

routes.AddRoutePath(HTTP.GET, "/nav-items", NavEndpoint.GetNavItems)
      .Produces<RazorComponentResult<NavItems>>()
      .AllowAnonymous()
      .WithSummary("Get the navigation menu");

routes.AddRoutePath(HTTP.GET, "/how-it-works", HowItWorksEndpoint.GetHowItWorks)
      .Produces<RazorComponentResult<HowItWorks>>()
      .AllowAnonymous()
      .WithSummary("Get a how-it-works page");

routes.AddRoutePath(HTTP.GET, "/counter", CounterEndpoint.GetCounter)
      .Produces<RazorComponentResult<Counter>>()
      .WithFeatureFlags(FeatureFlag.Counter)
      .AllowAnonymous()
      .WithSummary("Get a counter component");

routes.AddRoutePath(HTTP.POST, "/counter/update", CounterEndpoint.UpdateCounter)
      .Produces<RazorComponentResult<Counter>>()
      .WithFeatureFlags(FeatureFlag.Counter)
      .WithValidation<CounterValidator>()
      .AllowAnonymous()
      .WithSummary("Update a counter component");

routes.AddRoutePath(HTTP.GET, "/form", FormEndpoint.GetForm)
      .Produces<RazorComponentResult<Form>>()
      .WithFeatureFlags(FeatureFlag.Form)
      .AllowAnonymous()
      .WithSummary("Get an input form");

routes.AddRoutePath(HTTP.POST, "/form/submit", FormEndpoint.SubmitForm)
      .Produces<RazorComponentResult<Form>>()
      .WithFeatureFlags(FeatureFlag.FormSubmit)
      .WithValidation<FormValidator>()
      .AllowAnonymous()
      .WithSummary("Submit an input form");

routes.AddRoutePath(HTTP.POST, "/form/submitted", FormEndpoint.ToggleFormSubmittedModal)
      .Produces<RazorComponentResult<FormSubmitted>>()
      .WithFeatureFlags(FeatureFlag.FormSubmit)
      .AllowAnonymous()
      .WithSummary("Toggle a form submitted modal");

routes.AddRoutePath(HTTP.POST, "/form/email", FormEndpoint.ValidateEmail)
      .Produces<RazorComponentResult<FormEmail>>()
      .WithValidation<EmailValidator>()
      .AllowAnonymous()
      .WithSummary("Validate email");

routes.AddRoutePath(HTTP.GET, "/login", LoginEndpoint.GetLogin)
      .Produces<RazorComponentResult<LoginPage>>()
      .AllowAnonymous()
      .WithSummary("Get login form");

routes.AddRoutePath(HTTP.POST, "/login", LoginEndpoint.SubmitLogin)
      .Produces<IResult>()
      .Produces<RazorComponentResult<LoginForm>>()
      .WithValidation<LoginValidator>()
      .AllowAnonymous()
      .WithSummary("Login");

routes.AddRoutePath(HTTP.GET, "/logout", LogoutEndpoint.Logout)
      .Produces<IResult>()
      .RequireAuthorization()
      .WithSummary("Log out");

routes.AddRoutePath(HTTP.GET, "/admin/dashboard", AdminEndpoint.GetDashboard)
      .Produces<RazorComponentResult<AdminDashboard>>()
      .RequireAuthorization()
      .WithSummary("Get the admin dashboard");

routes.AddRoutePath(HTTP.POST, "/admin/feature-toggle", AdminEndpoint.ToggleFeatureFlag)
      .Produces<RazorComponentResult<AdminDashboard>>()
      .RequireAuthorization()
      .WithSummary("Toggles a feature on or off");

routes.AddRoutePath(HTTP.GET, "/users", UsersEndpoint.GetUsers)
      .Produces<RazorComponentResult<UsersList>>()
      .RequireAuthorization()
      .WithSummary("Get users list with search and pagination");

routes.AddRoutePath(HTTP.GET, "/users/create", UsersEndpoint.GetCreateUserForm)
      .Produces<RazorComponentResult<UserModal>>()
      .RequireAuthorization()
      .WithSummary("Get create user form");

routes.AddRoutePath(HTTP.GET, "/users/{id}/edit", UsersEndpoint.GetEditUserForm)
      .Produces<RazorComponentResult<UserModal>>()
      .RequireAuthorization()
      .WithSummary("Get edit user form");

routes.AddRoutePath(HTTP.POST, "/users", UsersEndpoint.CreateUser)
      .Produces<IResult>()
      .Produces<RazorComponentResult<UserModal>>()
      .WithValidation<CreateUserValidator>()
      .RequireAuthorization()
      .WithSummary("Create a new user");

routes.AddRoutePath(HTTP.POST, "/users/{id}", UsersEndpoint.UpdateUser)
      .Produces<IResult>()
      .Produces<RazorComponentResult<UserModal>>()
      .WithValidation<UpdateUserValidator>()
      .RequireAuthorization()
      .WithSummary("Update an existing user");

routes.AddRoutePath(HTTP.DELETE, "/users/{id}", UsersEndpoint.DeleteUser)
      .Produces<RazorComponentResult<UsersList>>()
      .RequireAuthorization()
      .WithSummary("Delete a user");

Console.WriteLine("App starting...");
app.MapGet("app", Component<App>);
app.Run();