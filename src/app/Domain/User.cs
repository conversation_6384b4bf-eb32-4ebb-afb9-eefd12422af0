using Newtonsoft.Json;
using Truckify.App.Infrastructure.Common;

namespace Truckify.App.Domain;

public class User
{
    [JsonConstructor]
    private User(string firstName, string lastName, string email)
    {
        FirstName = firstName;
        LastName = lastName;
        Email = email;
    }
    
    public string Id { get; private set; } = string.Empty;
    public string FirstName { get; private set; } = string.Empty;
    public string LastName { get; private set; } = string.Empty;
    public string Email { get; private set; } = string.Empty;

    public static Result<User> Create(string firstName, string lastName, string email)
    {
        if (string.IsNullOrWhiteSpace(firstName))
            return Result<User>.ErrorResult("First name cannot be empty");

        if (string.IsNullOrWhiteSpace(lastName))
            return Result<User>.ErrorResult("Last name cannot be empty");

        if (string.IsNullOrWhiteSpace(email))
            return Result<User>.ErrorResult("Email cannot be empty");

        var user = new User(firstName, lastName, email);
        return Result<User>.SuccessResult(user);
    }

    public Result UpdateDetails(string firstName, string lastName, string email)
    {
        if (string.IsNullOrWhiteSpace(firstName))
            return Result.ErrorResult("First name cannot be empty");

        if (string.IsNullOrWhiteSpace(lastName))
            return Result.ErrorResult("Last name cannot be empty");

        if (string.IsNullOrWhiteSpace(email))
            return Result.ErrorResult("Email cannot be empty");

        FirstName = firstName;
        LastName = lastName;
        Email = email;

        return Result.SuccessResult();
    }

    public string FullName => $"{FirstName} {LastName}";
}