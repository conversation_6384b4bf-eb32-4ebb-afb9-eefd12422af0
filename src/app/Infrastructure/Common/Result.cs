namespace Truckify.App.Infrastructure.Common;

/// <summary>
/// Represents the result of an operation that can either succeed with a value or fail with an error message.
/// </summary>
/// <typeparam name="T">The type of the value returned on success</typeparam>
public record Result<T>
{
    public bool Success { get; init; }
    public string? ErrorMessage { get; init; }
    public T? Value { get; init; }
    
    /// <summary>
    /// Creates a successful result with the specified value.
    /// </summary>
    /// <param name="value">The value to return</param>
    /// <returns>A successful result containing the value</returns>
    public static Result<T> SuccessResult(T value) => new() { Success = true, Value = value };
    
    /// <summary>
    /// Creates a failed result with the specified error message.
    /// </summary>
    /// <param name="message">The error message</param>
    /// <returns>A failed result containing the error message</returns>
    public static Result<T> ErrorResult(string message) => new() { Success = false, ErrorMessage = message };
}

/// <summary>
/// Represents the result of an operation that can either succeed or fail with an error message.
/// Use this when the operation doesn't return a specific value on success.
/// </summary>
public record Result
{
    public bool Success { get; init; }
    public string? ErrorMessage { get; init; }
    
    /// <summary>
    /// Creates a successful result.
    /// </summary>
    /// <returns>A successful result</returns>
    public static Result SuccessResult() => new() { Success = true };
    
    /// <summary>
    /// Creates a failed result with the specified error message.
    /// </summary>
    /// <param name="message">The error message</param>
    /// <returns>A failed result containing the error message</returns>
    public static Result ErrorResult(string message) => new() { Success = false, ErrorMessage = message };
}
