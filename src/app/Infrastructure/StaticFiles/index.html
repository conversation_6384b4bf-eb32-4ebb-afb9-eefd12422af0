<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Truckify - Dashboard</title>

    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@2.0.4"></script>
    <script src="https://unpkg.com/htmx.org@1.9.12/dist/ext/json-enc.js"></script>
    <script src="https://unpkg.com/htmx-ext-response-targets@2.0.2"></script>

    <!-- Fonts and Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,300;0,400;1,600&display=swap" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet" />

    <!-- Compiled CSS -->
    <link href="/App.compiled.css" rel="stylesheet">

    <!-- Custom Styles -->
    <style>
        * {
            font-family: 'Source Sans Pro', sans-serif;
        }
        :root {
            font-family: 'Source Sans Pro', sans-serif;
        }
        main#dashboard-main::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        main#dashboard-main::-webkit-scrollbar-thumb {
            border-radius: 9999px;
            background-color: rgb(156 163 175 / 0.75);
        }
        main#dashboard-main::-webkit-scrollbar-track {
            box-shadow: inset 0 0 5px rgb(156 163 175 / 0.75);
            border-radius: 10px;
        }
        nav#sidebar-nav {
            overflow-y: auto;
        }
        nav#sidebar-nav::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>
<body hx-ext="response-targets">
    <div id="error"></div>

    <div id="app"
         hx-get="/app"
         hx-trigger="revealed"
         hx-swap="outerHTML"
         hx-indicator="#loadingIndicator">
    </div>

    <!--<p class="htmx-indicator justify-center text-center" id="loadingIndicator">
        Loading...
    </p>-->

    <script src="/App.js"></script>
</body>