using Truckify.App.Infrastructure.Utils;

namespace Truckify.App.Infrastructure.Auth;

public static class Auth
{
    public static void AddAuthFeature(this WebApplicationBuilder builder)
    {
        builder.Services.AddAuthentication(Constants.TruckifyAuthCookie)
            .AddCookie(Constants.TruckifyAuthCookie, options =>
            {
                options.LoginPath = "/login";
                options.AccessDeniedPath = "/";
                options.Cookie.HttpOnly = true;
                options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
            });

        builder.Services.AddAuthorization();
    }

    public static IApplicationBuilder UseAuthFeature(this IApplicationBuilder app)
    {
        return app
            .UseAuthentication()
            .UseAuthorization();
    }
}
