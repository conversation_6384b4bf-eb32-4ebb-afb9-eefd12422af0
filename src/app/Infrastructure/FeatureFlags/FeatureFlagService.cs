using Truckify.App.Infrastructure.Utils;

namespace Truckify.App.Infrastructure.FeatureFlags;

public class FeatureFlagService(IConfiguration configuration)
{
    public bool IsFeatureEnabled(FeatureFlag featureFlag)
    {
        return false;
        return configuration.GetValue($"Features:{featureFlag}:Enabled", defaultValue: false);
    }

    public bool IsFeatureDisabled(FeatureFlag featureFlag)
    {
        return false;
        var enabled = configuration.GetValue($"Features:{featureFlag}:Enabled", defaultValue: true);
        return !enabled;
    }
}
