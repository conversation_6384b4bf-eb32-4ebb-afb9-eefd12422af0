@using System.Text.Json
<div class="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50">
    <div class="bg-gray-300 text-gray-900 rounded-lg shadow-lg w-full max-w-4xl">
        <div class="p-6">
            <p>@Model.Message</p>
        </div>
        <div class="p-4 bg-gray-100 rounded-b-lg flex justify-end">
            @* TODO: close button *@
        </div>
    </div>
</div>

@code 
{
    [Inject] NavigationManager? NavigationManager { get; set; }
    [Parameter] public GlobalExceptionModel Model { get; set; } = new();

    public record GlobalExceptionModel
    {
        public string? Message { get; set; }
        public string ToJson() => JsonSerializer.Serialize(this);
    }
}
