# Button Component

A flexible, reusable button component with multiple variants, sizes, and states inspired by BasecoatUI.

## Usage

```razor
@using Truckify.App.Infrastructure.Components

<!-- Basic button -->
<Button>Click me</Button>

<!-- Primary button with icon -->
<Button Variant="primary" IconLeft="fas fa-plus">
    Add User
</Button>

<!-- Secondary button -->
<Button Variant="secondary" Size="lg">
    Cancel
</Button>

<!-- Outline button -->
<Button Variant="outline" Size="sm">
    Edit
</Button>

<!-- Danger button -->
<Button Variant="danger" IconLeft="fas fa-trash">
    Delete
</Button>

<!-- Ghost button -->
<Button Variant="ghost" Size="xs">
    View Details
</Button>

<!-- Disabled button -->
<Button Disabled="true">
    Disabled
</Button>

<!-- Button with click handler -->
<Button OnClick="HandleClick">
    Click Handler
</Button>

<!-- Link button -->
<Button Type="link" Href="/users">
    Go to Users
</Button>

<!-- Button with HTMX attributes -->
<Button Variant="primary" 
        hx-get="/api/data"
        hx-target="#content">
    Load Data
</Button>
```

## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `ChildContent` | `RenderFragment?` | `null` | The content inside the button |
| `Variant` | `string` | `"primary"` | Button style variant |
| `Size` | `string` | `"md"` | Button size |
| `Type` | `string` | `"button"` | Button type or "link" for anchor |
| `Disabled` | `bool` | `false` | Whether the button is disabled |
| `Loading` | `bool` | `false` | Whether the button is in loading state |
| `IconLeft` | `string?` | `null` | Icon class for left icon |
| `IconRight` | `string?` | `null` | Icon class for right icon |
| `Href` | `string?` | `null` | URL for link buttons |
| `PreventDefault` | `bool` | `false` | Prevent default action for links |
| `OnClick` | `EventCallback<MouseEventArgs>` | - | Click event handler |

## Variants

- `primary` - Blue background, white text
- `secondary` - Gray background, dark text
- `outline` - Transparent background, colored border
- `ghost` - Transparent background, no border
- `danger` - Red background, white text
- `danger-outline` - Transparent background, red border
- `success` - Green background, white text
- `warning` - Yellow background, white text

## Sizes

- `xs` - Extra small (px-2 py-1, text-xs)
- `sm` - Small (px-3 py-1.5, text-sm)
- `md` - Medium (px-4 py-2, text-sm) - Default
- `lg` - Large (px-6 py-3, text-base)
- `xl` - Extra large (px-8 py-4, text-lg)

## Icons

Use FontAwesome classes for icons:
- `IconLeft="fas fa-plus"` - Plus icon on the left
- `IconRight="fas fa-arrow-right"` - Arrow icon on the right
- Icons automatically get proper spacing and sizing

## Examples in Context

### Form Actions
```razor
<div class="flex justify-end space-x-3">
    <Button Variant="secondary" onclick="closeModal()">
        Cancel
    </Button>
    <Button Variant="primary" Type="submit">
        Save Changes
    </Button>
</div>
```

### Table Actions
```razor
<div class="flex gap-2">
    <Button Variant="outline" Size="xs" IconLeft="fas fa-edit">
        Edit
    </Button>
    <Button Variant="danger-outline" Size="xs" IconLeft="fas fa-trash">
        Delete
    </Button>
</div>
```

### Navigation
```razor
<Button Type="link" Href="/dashboard" Variant="ghost">
    Back to Dashboard
</Button>
```

## TableActionButton Component

For table actions that need Razor expressions in HTMX attributes, use the specialized `TableActionButton` component:

```razor
@using Truckify.App.Infrastructure.Components

<!-- Edit action -->
<TableActionButton Icon="ri-pencil-line"
                   Variant="outline"
                   Size="sm"
                   Title="Edit user"
                   HxGet="@($"/users/{user.Id}/edit")"
                   HxTarget="body"
                   HxSwap="beforeend" />

<!-- Delete action -->
<TableActionButton Icon="ri-delete-bin-6-line"
                   Variant="danger-outline"
                   Size="sm"
                   Title="Delete user"
                   HxDelete="@($"/users/{user.Id}")"
                   HxTarget="#users-list"
                   HxSwap="outerHTML"
                   HxConfirm="Are you sure you want to delete this user?" />
```

### TableActionButton Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `Icon` | `string` | Icon class (e.g., "ri-pencil-line") |
| `Variant` | `string` | Button variant (outline, danger-outline, etc.) |
| `Size` | `string` | Button size (xs, sm, md, lg, xl) |
| `Title` | `string` | Tooltip text |
| `HxGet` | `string?` | HTMX GET URL |
| `HxPost` | `string?` | HTMX POST URL |
| `HxPut` | `string?` | HTMX PUT URL |
| `HxDelete` | `string?` | HTMX DELETE URL |
| `HxTarget` | `string?` | HTMX target selector |
| `HxSwap` | `string?` | HTMX swap strategy |
| `HxConfirm` | `string?` | HTMX confirmation message |
