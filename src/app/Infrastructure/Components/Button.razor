@using Microsoft.AspNetCore.Components.Web

@if (Type == "link")
{
    <a href="@Href" 
       class="@GetButtonClasses()" 
       @attributes="AdditionalAttributes"
       @onclick="OnClick"
       @onclick:preventDefault="PreventDefault">
        @if (IconOnly)
        {
            <i class="@(IconLeft ?? IconRight) @GetIconClasses()"></i>
        }
        else
        {
            @if (!string.IsNullOrEmpty(IconLeft))
            {
                <i class="@IconLeft @GetIconClasses()"></i>
            }
            @ChildContent
            @if (!string.IsNullOrEmpty(IconRight))
            {
                <i class="@IconRight @GetIconClasses()"></i>
            }
        }
    </a>
}
else
{
    <button type="@Type" 
            class="@GetButtonClasses()" 
            disabled="@Disabled"
            @attributes="AdditionalAttributes"
            @onclick="OnClick">
        @if (IconOnly)
        {
            <i class="@(IconLeft ?? IconRight) @GetIconClasses()"></i>
        }
        else
        {
            @if (!string.IsNullOrEmpty(IconLeft))
            {
                <i class="@IconLeft @GetIconClasses()"></i>
            }
            @ChildContent
            @if (!string.IsNullOrEmpty(IconRight))
            {
                <i class="@IconRight @GetIconClasses()"></i>
            }
        }
    </button>
}

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public string Variant { get; set; } = "primary";
    [Parameter] public string Size { get; set; } = "md";
    [Parameter] public string Type { get; set; } = "button";
    [Parameter] public bool Disabled { get; set; } = false;
    [Parameter] public bool Loading { get; set; } = false;
    [Parameter] public string? IconLeft { get; set; }
    [Parameter] public string? IconRight { get; set; }
    [Parameter] public bool IconOnly { get; set; } = false;
    [Parameter] public string? Href { get; set; }
    [Parameter] public bool PreventDefault { get; set; } = false;
    [Parameter] public EventCallback<MouseEventArgs> OnClick { get; set; }
    [Parameter(CaptureUnmatchedValues = true)] public Dictionary<string, object>? AdditionalAttributes { get; set; }

    private string GetButtonClasses()
    {
        // Base classes matching BasecoatUI exactly with better icon alignment
        var baseClasses = "inline-flex items-center justify-center font-medium cursor-pointer select-none border focus:outline-none focus:ring-0 disabled:opacity-50 disabled:pointer-events-none transition-colors leading-none";

        var sizeClasses = IconOnly ? Size switch
        {
            "xs" => "w-6 h-6 text-xs rounded-md",
            "sm" => "w-8 h-8 text-sm rounded-md",
            "md" => "w-10 h-10 text-sm rounded-md",
            "lg" => "w-12 h-12 text-base rounded-md",
            "xl" => "w-14 h-14 text-lg rounded-md",
            _ => "w-8 h-8 text-sm rounded-md"
        } : Size switch
        {
            "xs" => "px-2 py-1 text-xs rounded-md",
            "sm" => "px-3 py-1.5 text-sm rounded-md",
            "md" => "px-4 py-2 text-sm rounded-md",
            "lg" => "px-6 py-3 text-base rounded-md",
            "xl" => "px-8 py-4 text-lg rounded-md",
            _ => "px-4 py-2 text-sm rounded-md"
        };

        var variantClasses = Variant switch
        {
            // Matching BasecoatUI .btn-primary exactly
            "primary" => "bg-gray-900 border-gray-900 text-white hover:bg-gray-800 hover:border-gray-800 focus:bg-gray-800 focus:border-gray-800 active:bg-gray-700 active:border-gray-700",

            // Matching BasecoatUI .btn-secondary exactly
            "secondary" => "bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:bg-gray-50 focus:border-gray-400 active:bg-gray-100 active:border-gray-500",

            // Matching BasecoatUI .btn-outline exactly
            "outline" => "bg-transparent border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:bg-gray-50 focus:border-gray-400 active:bg-gray-100 active:border-gray-500",

            // Ghost variant
            "ghost" => "bg-transparent border-transparent text-gray-700 hover:bg-gray-100 focus:bg-gray-100 active:bg-gray-200",

            // Danger variants
            "danger" => "bg-red-600 border-red-600 text-white hover:bg-red-700 hover:border-red-700 focus:bg-red-700 focus:border-red-700 active:bg-red-800 active:border-red-800",
            "danger-outline" => "bg-transparent border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 focus:bg-red-50 focus:border-red-400 active:bg-red-100 active:border-red-500",

            // Success and warning
            "success" => "bg-green-600 border-green-600 text-white hover:bg-green-700 hover:border-green-700 focus:bg-green-700 focus:border-green-700 active:bg-green-800 active:border-green-800",
            "warning" => "bg-amber-500 border-amber-500 text-white hover:bg-amber-600 hover:border-amber-600 focus:bg-amber-600 focus:border-amber-600 active:bg-amber-700 active:border-amber-700",

            _ => "bg-gray-900 border-gray-900 text-white hover:bg-gray-800 hover:border-gray-800 focus:bg-gray-800 focus:border-gray-800 active:bg-gray-700 active:border-gray-700"
        };

        var loadingClasses = Loading ? "cursor-wait" : "";
        var disabledClasses = Disabled ? "opacity-50 cursor-not-allowed" : "";

        return $"{baseClasses} {sizeClasses} {variantClasses} {loadingClasses} {disabledClasses}".Trim();
    }

    private string GetIconClasses()
    {
        if (IconOnly)
        {
            var iconSizeClass = Size switch
            {
                "xs" => "text-xs",
                "sm" => "text-sm",
                "md" => "text-sm",
                "lg" => "text-base",
                "xl" => "text-lg",
                _ => "text-sm"
            };
            return $"{iconSizeClass} flex-shrink-0".Trim();
        }

        var spacing = ChildContent != null ?
            (IconLeft != null ? "mr-2" : IconRight != null ? "ml-2" : "") : "";

        var sizeClass = Size switch
        {
            "xs" => "text-xs",
            "sm" => "text-sm",
            "md" => "text-sm",
            "lg" => "text-base",
            "xl" => "text-lg",
            _ => "text-sm"
        };

        // Better alignment for icons - use text size instead of fixed width/height for better alignment
        var alignmentClass = "flex-shrink-0 inline-flex items-center";

        return $"{sizeClass} {spacing} {alignmentClass}".Trim();
    }
}
