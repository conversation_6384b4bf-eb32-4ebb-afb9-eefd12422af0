@using System.Text.Json

<div id="toast-container" class="relative m-2 my-8 max-w-sm rounded-lg bg-gray-600 px-12 py-6 shadow-md">
    <button class="absolute top-0 right-0 p-4 text-gray-400">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
             class="h-5 w-4">
            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
        </svg>
    </button>
    <p class="relative mb-1 text-sm font-medium">
        <span class="absolute -left-7 flex h-5 w-5 items-center justify-center rounded-xl bg-red-400 text-white">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                 stroke="currentColor" class="h-3 w-3">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
            </svg>
        </span>
        <span class="text-gray-50">Save Failed!</span>
    </p>
    <p class="text-sm text-gray-200">@Model.Message</p>
</div>

@*<div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"
     hx-on::load="removeElementWithDelay('#toast-container', 3000)">
    <div class="relative m-2 my-8 max-w-sm rounded-lg @GetBackgroundColor() px-12 py-6 shadow-md">
        <button hx-on:click="removeElement('#toast-container')"
                class="absolute top-0 right-0 p-4 text-gray-400 hover:text-white">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                 stroke="currentColor" class="h-5 w-4">
                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
            </svg>
        </button>
        <p class="relative mb-1 text-sm font-medium">
            <span class="absolute -left-7 flex h-5 w-5 items-center justify-center rounded-xl @GetIconBackgroundColor() text-white">
                @if (Model.IsError)
                {
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                         stroke="currentColor" class="h-3 w-3">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                }
                else
                {
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                         stroke="currentColor" class="h-3 w-3">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5"/>
                    </svg>
                }
            </span>
            <span class="text-gray-50">@GetTitle()</span>
        </p>
        <p class="text-sm text-gray-200">@Model.Message</p>
    </div>
</div>*@

@code {
    [Parameter] public ToastModel Model { get; set; } = new();

    public record ToastModel
    {
        public string Message { get; set; } = string.Empty;
        public bool IsError { get; set; } = false;
    }

    private string GetBackgroundColor() => Model.IsError ? "bg-red-600" : "bg-green-600";
    private string GetIconBackgroundColor() => Model.IsError ? "bg-red-400" : "bg-green-400";
    private string GetTitle() => Model.IsError ? "Error!" : "Success!";
}
