@* Table Action Button Component - Handles Razor expressions via parameters *@

<button type="@Type"
        class="@GetButtonClasses()"
        disabled="@Disabled"
        title="@Title"
        hx-get="@HxGet"
        hx-post="@HxPost"
        hx-put="@HxPut"
        hx-delete="@HxDelete"
        hx-target="@HxTarget"
        hx-swap="@HxSwap"
        hx-confirm="@HxConfirm"
        @attributes="AdditionalAttributes">
    <i class="@Icon @GetIconClasses()"></i>
</button>

@code {
    [Parameter] public string Icon { get; set; } = string.Empty;
    [Parameter] public string Variant { get; set; } = "outline";
    [Parameter] public string Size { get; set; } = "sm";
    [Parameter] public string Type { get; set; } = "button";
    [Parameter] public bool Disabled { get; set; } = false;
    [Parameter] public string Title { get; set; } = string.Empty;

    // HTMX Parameters
    [Parameter] public string? HxGet { get; set; }
    [Parameter] public string? HxPost { get; set; }
    [Parameter] public string? HxPut { get; set; }
    [Parameter] public string? HxDelete { get; set; }
    [Parameter] public string? HxTarget { get; set; }
    [Parameter] public string? HxSwap { get; set; }
    [Parameter] public string? HxConfirm { get; set; }

    [Parameter(CaptureUnmatchedValues = true)] public Dictionary<string, object>? AdditionalAttributes { get; set; }

    private string GetButtonClasses()
    {
        // Base classes matching BasecoatUI exactly
        var baseClasses = "inline-flex items-center justify-center font-medium cursor-pointer select-none border focus:outline-none focus:ring-0 transition-colors";
        
        var sizeClasses = Size switch
        {
            "xs" => "w-6 h-6 text-xs rounded-md",
            "sm" => "w-8 h-8 text-sm rounded-md", 
            "md" => "w-10 h-10 text-sm rounded-md",
            "lg" => "w-12 h-12 text-base rounded-md",
            "xl" => "w-14 h-14 text-lg rounded-md",
            _ => "w-8 h-8 text-sm rounded-md"
        };

        var variantClasses = Variant switch
        {
            // Matching BasecoatUI .btn-primary exactly
            "primary" => "bg-gray-900 border-gray-900 text-white hover:bg-gray-800 hover:border-gray-800 focus:bg-gray-800 focus:border-gray-800 active:bg-gray-700 active:border-gray-700",
            
            // Matching BasecoatUI .btn-secondary exactly  
            "secondary" => "bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:bg-gray-50 focus:border-gray-400 active:bg-gray-100 active:border-gray-500",
            
            // Matching BasecoatUI .btn-outline exactly
            "outline" => "bg-transparent border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:bg-gray-50 focus:border-gray-400 active:bg-gray-100 active:border-gray-500",
            
            // Ghost variant
            "ghost" => "bg-transparent border-transparent text-gray-700 hover:bg-gray-100 focus:bg-gray-100 active:bg-gray-200",
            
            // Danger variants
            "danger" => "bg-red-600 border-red-600 text-white hover:bg-red-700 hover:border-red-700 focus:bg-red-700 focus:border-red-700 active:bg-red-800 active:border-red-800",
            "danger-outline" => "bg-transparent border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 focus:bg-red-50 focus:border-red-400 active:bg-red-100 active:border-red-500",
            
            // Success and warning
            "success" => "bg-green-600 border-green-600 text-white hover:bg-green-700 hover:border-green-700 focus:bg-green-700 focus:border-green-700 active:bg-green-800 active:border-green-800",
            "warning" => "bg-amber-500 border-amber-500 text-white hover:bg-amber-600 hover:border-amber-600 focus:bg-amber-600 focus:border-amber-600 active:bg-amber-700 active:border-amber-700",
            
            _ => "bg-transparent border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:bg-gray-50 focus:border-gray-400 active:bg-gray-100 active:border-gray-500"
        };

        var disabledClasses = Disabled ? "opacity-50 cursor-not-allowed" : "";

        return $"{baseClasses} {sizeClasses} {variantClasses} {disabledClasses}".Trim();
    }

    private string GetIconClasses()
    {
        var sizeClass = Size switch
        {
            "xs" => "text-xs",
            "sm" => "text-sm", 
            "md" => "text-sm",
            "lg" => "text-base",
            "xl" => "text-lg",
            _ => "text-sm"
        };
        
        return $"{sizeClass} flex-shrink-0".Trim();
    }
}
