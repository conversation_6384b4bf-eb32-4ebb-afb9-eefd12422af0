<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <!--<UseAppHost>false</UseAppHost>-->
        <RootNamespace>Truckify.App</RootNamespace>
    </PropertyGroup>
    

    <ItemGroup>
        <PackageReference Include="FluentValidation" Version="12.0.0" />
        <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
        <PackageReference Include="RavenDB.Client" Version="7.0.4" />
    </ItemGroup>

    <ItemGroup>
        <None Update="Infrastructure/StaticFiles/**">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <UpToDateCheckBuilt Include="Infrastructure/StaticFiles/App.css" Set="Css" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Layout\" />
    </ItemGroup>

    <ItemGroup>
      <_ContentIncludedByDefault Remove="Features\Exceptions\GlobalException.razor" />
    </ItemGroup>

    <Target Name="Tailwind" BeforeTargets="Build">
        <Exec Command="npm run css:build" />
    </Target>

</Project>
