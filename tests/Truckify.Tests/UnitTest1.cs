using Truckify.App.Domain;
using Truckify.App.Infrastructure.Common;

namespace Truckify.Tests;

public class UserTests
{
    [Fact]
    public void User_Create_WithValidData_ReturnsSuccessResult()
    {
        // Arrange
        var firstName = "John";
        var lastName = "Doe";
        var email = "<EMAIL>";

        // Act
        var result = User.Create(firstName, lastName, email);

        // Assert
        Assert.True(result.Success);
        Assert.Null(result.ErrorMessage);
        Assert.NotNull(result.Value);
        Assert.Equal(firstName, result.Value.FirstName);
        Assert.Equal(lastName, result.Value.LastName);
        Assert.Equal(email, result.Value.Email);
    }

    [Fact]
    public void User_Create_WithEmptyFirstName_ReturnsErrorResult()
    {
        // Arrange
        var firstName = "";
        var lastName = "Doe";
        var email = "<EMAIL>";

        // Act
        var result = User.Create(firstName, lastName, email);

        // Assert
        Assert.False(result.Success);
        Assert.Equal("First name cannot be empty", result.ErrorMessage);
        Assert.Null(result.Value);
    }

    [Fact]
    public void User_Create_WithEmptyLastName_ReturnsErrorResult()
    {
        // Arrange
        var firstName = "John";
        var lastName = "";
        var email = "<EMAIL>";

        // Act
        var result = User.Create(firstName, lastName, email);

        // Assert
        Assert.False(result.Success);
        Assert.Equal("Last name cannot be empty", result.ErrorMessage);
        Assert.Null(result.Value);
    }

    [Fact]
    public void User_Create_WithEmptyEmail_ReturnsErrorResult()
    {
        // Arrange
        var firstName = "John";
        var lastName = "Doe";
        var email = "";

        // Act
        var result = User.Create(firstName, lastName, email);

        // Assert
        Assert.False(result.Success);
        Assert.Equal("Email cannot be empty", result.ErrorMessage);
        Assert.Null(result.Value);
    }

    [Fact]
    public void User_UpdateDetails_WithValidData_ReturnsSuccessResult()
    {
        // Arrange
        var userResult = User.Create("John", "Doe", "<EMAIL>");
        var user = userResult.Value!;
        var newFirstName = "Jane";
        var newLastName = "Smith";
        var newEmail = "<EMAIL>";

        // Act
        var result = user.UpdateDetails(newFirstName, newLastName, newEmail);

        // Assert
        Assert.True(result.Success);
        Assert.Null(result.ErrorMessage);
        Assert.Equal(newFirstName, user.FirstName);
        Assert.Equal(newLastName, user.LastName);
        Assert.Equal(newEmail, user.Email);
    }

    [Fact]
    public void User_UpdateDetails_WithEmptyFirstName_ReturnsErrorResult()
    {
        // Arrange
        var userResult = User.Create("John", "Doe", "<EMAIL>");
        var user = userResult.Value!;
        var originalFirstName = user.FirstName;

        // Act
        var result = user.UpdateDetails("", "Smith", "<EMAIL>");

        // Assert
        Assert.False(result.Success);
        Assert.Equal("First name cannot be empty", result.ErrorMessage);
        Assert.Equal(originalFirstName, user.FirstName); // Should remain unchanged
    }
}

public class ResultTests
{
    [Fact]
    public void Result_SuccessResult_CreatesSuccessfulResult()
    {
        // Arrange
        var value = "test value";

        // Act
        var result = Result<string>.SuccessResult(value);

        // Assert
        Assert.True(result.Success);
        Assert.Null(result.ErrorMessage);
        Assert.Equal(value, result.Value);
    }

    [Fact]
    public void Result_ErrorResult_CreatesFailedResult()
    {
        // Arrange
        var errorMessage = "Something went wrong";

        // Act
        var result = Result<string>.ErrorResult(errorMessage);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(errorMessage, result.ErrorMessage);
        Assert.Null(result.Value);
    }

    [Fact]
    public void Result_NonGeneric_SuccessResult_CreatesSuccessfulResult()
    {
        // Act
        var result = Result.SuccessResult();

        // Assert
        Assert.True(result.Success);
        Assert.Null(result.ErrorMessage);
    }

    [Fact]
    public void Result_NonGeneric_ErrorResult_CreatesFailedResult()
    {
        // Arrange
        var errorMessage = "Operation failed";

        // Act
        var result = Result.ErrorResult(errorMessage);

        // Assert
        Assert.False(result.Success);
        Assert.Equal(errorMessage, result.ErrorMessage);
    }
}
